name: Translation Tools Tests

on:
  push:
    branches: [ main, dev ]
  pull_request:
    branches: [ main, dev ]
  workflow_dispatch:

jobs:
  test-translation-tools:
    name: Test Translation Management Tools
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        flutter-channel: ['stable']
        flutter-version: ['3.19.0']
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ matrix.flutter-version }}
          channel: ${{ matrix.flutter-channel }}
          cache: true

      - name: Cache Dart packages
        uses: actions/cache@v3
        with:
          path: ~/.pub-cache
          key: ${{ runner.os }}-pub-cache-${{ hashFiles('**/pubspec.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pub-cache-

      - name: Install dependencies
        run: flutter pub get

      - name: Check Flutter installation
        run: |
          flutter --version
          dart --version

      - name: Create lib/l10n directory for validation tests
        run: |
          mkdir -p lib/l10n
          # Copy test ARB files to lib/l10n for validation testing
          cp test/tools/translation_management/test_data/app_en.arb lib/l10n/
          cp test/tools/translation_management/test_data/app_hi.arb lib/l10n/

      - name: Run translation management tool tests
        run: |
          echo "Running translation management unit tests..."
          flutter test test/tools/translation_management/ --reporter=expanded

      - name: Run translation validator with strict mode
        run: |
          echo "Running translation validation in strict mode..."
          dart run tools/translation_management/validate_translations.dart --strict --directory lib/l10n

      - name: Test translation importer dry-run
        run: |
          echo "Testing translation importer in dry-run mode..."
          dart run tools/import_translations.dart --help
          dart run tools/import_translations.dart test/tools/translation_management/test_data/sample_translations.csv --dry-run --output-dir lib/l10n

      - name: Cleanup test artifacts
        if: always()
        run: |
          rm -rf lib/l10n/app_*.arb

  lint-translation-tools:
    name: Lint Translation Tools
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.0'
          channel: 'stable'
          cache: true

      - name: Install dependencies
        run: flutter pub get

      - name: Analyze translation tools
        run: |
          echo "Analyzing translation management tools..."
          dart analyze tools/translation_management/
          dart analyze tools/import_translations.dart
          dart analyze tools/arb_organizer.dart

      - name: Check tool script executability
        run: |
          echo "Checking tool scripts..."
          ls -la tools/translation_management/validate_translations.dart
          ls -la tools/import_translations.dart
          head -1 tools/translation_management/validate_translations.dart
          head -1 tools/import_translations.dart

  test-edge-cases:
    name: Test Edge Cases
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.0'
          channel: 'stable'
          cache: true

      - name: Install dependencies
        run: flutter pub get

      - name: Test validator with empty directory
        run: |
          mkdir -p temp_empty_l10n
          echo "Testing validator with empty directory (should fail gracefully)..."
          dart run tools/translation_management/validate_translations.dart --directory temp_empty_l10n || echo "Expected failure - no reference file"
          rm -rf temp_empty_l10n

      - name: Test validator with malformed ARB files
        run: |
          mkdir -p temp_test_l10n
          echo "Testing validator with malformed ARB files..."
          
          # Copy reference file
          cp test/tools/translation_management/test_data/app_en.arb temp_test_l10n/
          
          # Copy malformed files
          cp test/tools/translation_management/test_data/invalid_syntax.arb temp_test_l10n/
          cp test/tools/translation_management/test_data/missing_keys.arb temp_test_l10n/
          cp test/tools/translation_management/test_data/placeholder_errors.arb temp_test_l10n/
          
          # Run validation (should detect errors)
          dart run tools/translation_management/validate_translations.dart --directory temp_test_l10n --quiet || echo "Expected validation failures detected"
          
          rm -rf temp_test_l10n

      - name: Test importer with invalid files
        run: |
          echo "Testing importer with non-existent file..."
          dart run tools/import_translations.dart /nonexistent/file.csv --dry-run || echo "Expected failure - file not found"
