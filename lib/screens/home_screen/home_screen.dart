import 'package:flutter/material.dart';
import 'package:railops/widgets/index.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      appBar: CustomAppBar(
        title: 'Home',
        actions: [
          // Compact language selector in AppBar
          Padding(
            padding: EdgeInsets.only(right: 8.0),
            child: LanguageSelector(isCompact: true, showLabel: false),
          ),
        ],
      ),
      drawer: CustomDrawer(),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Text(
              'Home Screen',
              style: TextStyle(fontSize: 24),
            )
          ],
        ),
      ),
    );
  }
}
