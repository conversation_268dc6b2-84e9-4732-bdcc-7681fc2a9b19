import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:railops/constants/index.dart';
import 'package:railops/services/api_services/index.dart';


class ApiService {
  static Future<dynamic> get(String endpoint, dynamic body) async {
    Map<String, String> headers = {
      'Content-Type': 'application/json; charset=UTF-8',
    };
    if (body != null && body.containsKey('token') && body['token'] != null) {
      headers['Authorization'] = 'Bearer ${body['token']}';
    }
    final response = await http.get(
      Uri.parse('${ApiConstant.baseUrl}$endpoint'),
      headers: headers,
    );
    return _handleResponse(response);
  }

  static Future<dynamic> post(String endpoint, dynamic body) async {
    body['token'] = body['token'] ?? body.remove('token');
    final headers = <String, String>{
      'Content-Type': 'application/json; charset=UTF-8',
    };
    if (body['token'] != null && body['token'].isNotEmpty) {
      headers['Authorization'] = 'Bearer ${body['token']}';
    }  
    final response = await http.post(
      Uri.parse('${ApiConstant.baseUrl}$endpoint'),
      headers: headers,
      body: jsonEncode(body),
    );
    return _handleResponse(response);
  }

  static Future<dynamic> put(String endpoint, dynamic body) async {
    final response = await http.put(
      Uri.parse('${ApiConstant.baseUrl}$endpoint'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer ${body['token']}',
      },
      body: jsonEncode(body),
    );
    return _handleResponse(response);
  }

  static Future<dynamic> delete(String endpoint, dynamic body) async {
    final response = await http.delete(
      Uri.parse('${ApiConstant.baseUrl}$endpoint'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer ${body['token']}',
      },
      body: jsonEncode(body),
    );
    return _handleResponse(response);
  }

  static dynamic _handleResponse(http.Response response) {
    if (response.headers['content-type']?.contains('application/pdf') ?? false) {
      return response.bodyBytes;
    } 
    else if (response.statusCode == 204) {
      return "deleted sucessfully";
    } 
    else {
      final jsonResponse = json.decode(response.body);
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return jsonResponse;
      } else {
        if (jsonResponse.containsKey('details')) {
          throw DetailedApiException.fromJson(response.statusCode, jsonResponse);
        } else {
          throw ApiException.fromJson(response.statusCode, jsonResponse);
        }
      }
    }
  }
}
