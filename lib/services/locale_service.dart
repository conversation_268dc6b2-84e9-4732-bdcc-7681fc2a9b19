import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocaleService extends ChangeNotifier {
  static LocaleService? _instance;
  Locale _currentLocale = const Locale('en');
  static const String _localeKey = 'selected_locale';

  // Private constructor
  LocaleService._();

  // Singleton instance getter
  static LocaleService get instance {
    _instance ??= LocaleService._();
    return _instance!;
  }

  // Instance getter for current locale
  Locale get currentLocale => _currentLocale;

  // Instance method to initialize
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    final savedLocale = prefs.getString(_localeKey);
    if (savedLocale != null) {
      _currentLocale = Locale(savedLocale);
      notifyListeners();
    }
  }

  // Instance method to change locale with notifyListeners
  Future<void> changeLocale(Locale newLocale) async {
    if (_currentLocale != newLocale) {
      _currentLocale = newLocale;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_localeKey, newLocale.languageCode);
      notifyListeners(); // This will trigger UI rebuild
    }
  }

  /// Get all supported locales
  static List<Locale> getSupportedLocales() {
    return const [
      Locale('en'), // English
      Locale('hi'), // Hindi
      Locale('bn'), // Bengali
      Locale('as'), // Assamese
      Locale('pa'), // Punjabi
      Locale('mr'), // Marathi
      Locale('kn'), // Kannada
      Locale('ta'), // Tamil
      Locale('te'), // Telugu
      Locale('ml'), // Malayalam
    ];
  }

  /// Get language name in native script
  static String getLanguageName(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'hi':
        return 'हिंदी';
      case 'bn':
        return 'বাংলা';
      case 'as':
        return 'অসমীয়া';
      case 'pa':
        return 'ਪੰਜਾਬੀ';
      case 'mr':
        return 'मराठी';
      case 'kn':
        return 'ಕನ್ನಡ';
      case 'ta':
        return 'தமிழ்';
      case 'te':
        return 'తెలుగు';
      case 'ml':
        return 'മലയാളം';
      default:
        return 'English';
    }
  }
}
