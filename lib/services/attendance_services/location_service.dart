import 'package:railops/models/index.dart';
import 'package:railops/services/api_services/index.dart';
import 'package:railops/types/attendance_types/onboarding_response.dart';

class OnboardingService {
  String _token = '';

  OnboardingService() {
    _getToken();
  }

  Future<void> _getToken() async {
    final userModel = UserModel();
    await userModel.loadUserData();
    _token = userModel.token;
  }

  static Future<OnboardingResponse> fetchOnboardingDetails(
    final String trainNumber,
    final String date,
  ) async {
    try {
      final responseJson = await ApiService.get(
        // '/api/onboarding_details_by_nearby_stations/',
        "/microservice/train/location/?train_number=${trainNumber}&date=${date}",
        {
          'token': await _fetchToken(),
          if (trainNumber.isNotEmpty) 'train_number': trainNumber,
          if (date.isNotEmpty) 'date': date,
        },
      );

      return OnboardingResponse.fromJson(responseJson);
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw Exception('Error occurred while fetching onboarding details: $e');
    }
  }

  static Future<String> _fetchToken() async {
    final userModel = UserModel();
    await userModel.loadUserData();
    return userModel.token;
  }
}
