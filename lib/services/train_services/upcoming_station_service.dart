import 'dart:convert';
import 'package:railops/services/api_services/api_exception.dart';
import 'package:railops/services/api_services/api_service.dart';
import 'package:railops/types/train_types/upcoming_station_response.dart';

class UpcomingStationService {
  // Calls the API to fetch upcoming station details
  static Future<UpcomingStationResponse> fetchUpcomingStationDetails({
    required String lat,
    required String lng,
    required String token,
  }) async {
    try {
      final responseJson = await ApiService.post(
        '/api/onboarding_details_popup/', 
        {
          'lat': lat,
          'lng': lng,
          'token': token,
        },
      );
      print(responseJson);

      return UpcomingStationResponse.fromJson(responseJson);
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw Exception(e);
    }
  }
}
