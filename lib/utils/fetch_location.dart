import 'dart:io';
import 'dart:math';

import 'package:railops/services/location_services/location_services.dart';
import 'package:workmanager/workmanager.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';

class LocationTracker {
  static const fetchLocationTask = "fetchLocationTask";
  static const notificationChannelId = 'location_service_channel';
  static const notificationChannelName = 'Location Service';
  static const notificationId = 888;

  static Future<void> initialize() async {
    await Workmanager().initialize(callbackDispatcher, isInDebugMode: false);
  }

  static Future<bool> checkAndRequestPermissions() async {
    final locationStatus = await Permission.location.request();
    if (!locationStatus.isGranted) {
      return false;
    }

    if (Platform.isAndroid) {
      final backgroundStatus = await Permission.locationAlways.request();
      if (!backgroundStatus.isGranted) {
        return false;
      }
    }

    final notificationStatus = await Permission.notification.request();
    if (!notificationStatus.isGranted) {
      return false;
    }

    return true;
  }

  static Future<void> startTracking() async {
    
    await Workmanager().registerPeriodicTask(
      "periodicLocationTask",
      fetchLocationTask,
      frequency: const Duration(minutes: 15),
      existingWorkPolicy: ExistingWorkPolicy.replace,
      constraints: Constraints(
        networkType: NetworkType.connected,
        requiresBatteryNotLow: false,
        requiresCharging: false,
        requiresDeviceIdle: false,
        requiresStorageNotLow: false,
      ),
      backoffPolicy: BackoffPolicy.exponential,
      backoffPolicyDelay: const Duration(minutes: 1),
    );

    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isTracking', true);
  }

  static Future<void> stopTracking() async {
    await Workmanager().cancelAll();
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isTracking', false);
  }

}

@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    if (task == LocationTracker.fetchLocationTask) {
      try {
        final prefs = await SharedPreferences.getInstance();
        final isTracking = prefs.getBool('isTracking') ?? false;
        
        if (!isTracking) {
          return Future.value(false);
        }

        // Request location permission again as it might have been revoked
        final locationPermission = await Geolocator.checkPermission();
        if (locationPermission == LocationPermission.denied ||
            locationPermission == LocationPermission.deniedForever) {
          return Future.value(false);
        }

        Position position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.medium,
        );

        String? token = prefs.getString('authToken');
        if (token == null) {
          print('Error: No auth token found.');
          return Future.value(false);
        }

        int retryCount = 0;
        bool success = false;
        while (retryCount < 3 && !success) {
          try {
            String responseMessage = await LocationService.addCurrentUserLocation(
              token,
              position.latitude.toString(),
              position.longitude.toString(),
            );
            print('Server Response: $responseMessage');
            success = true;
          } catch (e) {
            retryCount++;
            if (retryCount < 3) {
              await Future.delayed(Duration(seconds: pow(2, retryCount).toInt()));
            }
          }
        }

        return Future.value(success);
      } catch (e) {
        print('Error occurred while fetching location or sending data: $e');
        return Future.value(false);
      }
    }
    return Future.value(true);
  });
}